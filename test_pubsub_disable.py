#!/usr/bin/env python3
"""
Test script to verify the --pubsub.disable argument works correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from compute.utils.parser import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def test_pubsub_disable_argument():
    """Test that the --pubsub.disable argument is properly parsed."""
    
    # Test with pubsub disabled
    parser = ComputeArgPaser(description="Test pubsub disable argument")
    
    # Simulate command line arguments
    test_args_disabled = ["--pubsub.disable"]
    config_disabled = parser.parse_args(test_args_disabled)
    
    print("Testing --pubsub.disable flag:")
    print(f"  pubsub_disabled = {config_disabled.pubsub_disabled}")
    assert config_disabled.pubsub_disabled == True, "pubsub_disabled should be True when flag is provided"
    
    # Test without pubsub disabled (default)
    test_args_enabled = []
    config_enabled = parser.parse_args(test_args_enabled)
    
    print("Testing default behavior (no flag):")
    print(f"  pubsub_disabled = {config_enabled.pubsub_disabled}")
    assert config_enabled.pubsub_disabled == False, "pubsub_disabled should be False by default"
    
    print("✅ All tests passed!")

if __name__ == "__main__":
    test_pubsub_disable_argument()
