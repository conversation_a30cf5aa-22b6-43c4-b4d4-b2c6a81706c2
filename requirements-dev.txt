#
# This file is autogenerated by pip-compile with Python 3.12
# by the following command:
#
#    pip-compile --extra=dev --output-file=requirements-dev.txt pyproject.toml
#
aiohappyeyeballs==2.4.8
    # via aiohttp
aiohttp==3.10.11
    # via
    #   bittensor
    #   bittensor-cli
aiosignal==1.3.2
    # via aiohttp
allure-pytest==2.13.5
    # via NI-Compute (pyproject.toml)
allure-python-commons==2.13.5
    # via allure-pytest
altgraph==0.17.4
    # via pyinstaller
annotated-types==0.7.0
    # via pydantic
anyio==4.8.0
    # via starlette
async-property==0.2.2
    # via bittensor-cli
async-substrate-interface==1.0.3
    # via
    #   bittensor
    #   bittensor-cli
asyncstdlib==3.13.0
    # via
    #   async-substrate-interface
    #   bittensor
at==0.0.3
    # via NI-Compute (pyproject.toml)
attrs==25.1.0
    # via
    #   aiohttp
    #   allure-python-commons
backoff==2.2.1
    # via bittensor-cli
base58==2.1.1
    # via scalecodec
bcrypt==4.3.0
    # via paramiko
bittensor[cli]==9.0.0
    # via NI-Compute (pyproject.toml)
bittensor-cli==9.1.0
    # via bittensor
bittensor-commit-reveal==0.2.0
    # via bittensor
bittensor-wallet==3.0.4
    # via
    #   async-substrate-interface
    #   bittensor
    #   bittensor-cli
blake3==1.0.4
    # via NI-Compute (pyproject.toml)
bt-decode==0.5.0a2
    # via async-substrate-interface
build==1.2.2.post1
    # via pip-tools
cachetools==5.5.2
    # via google-auth
certifi==2025.1.31
    # via
    #   requests
    #   sentry-sdk
cffi==1.17.1
    # via
    #   cryptography
    #   pynacl
cfgv==3.4.0
    # via pre-commit
charset-normalizer==3.4.1
    # via requests
click==8.1.8
    # via
    #   pip-tools
    #   typer
    #   uvicorn
    #   wandb
colorama==0.4.6
    # via bittensor
coverage[toml]==7.6.12
    # via pytest-cov
cryptography==43.0.1
    # via
    #   NI-Compute (pyproject.toml)
    #   bittensor-wallet
    #   paramiko
cytoolz==1.0.1
    # via eth-utils
decorator==5.2.1
    # via retry
defusedxml==0.7.1
    # via ipwhois
distlib==0.3.9
    # via virtualenv
dnspython==2.7.0
    # via ipwhois
docker==7.0.0
    # via NI-Compute (pyproject.toml)
docker-pycreds==0.4.0
    # via wandb
eth-hash==0.7.1
    # via eth-utils
eth-typing==5.2.0
    # via eth-utils
eth-utils==2.2.2
    # via bittensor-wallet
fastapi==0.110.3
    # via bittensor
filelock==3.17.0
    # via
    #   torch
    #   triton
    #   virtualenv
frozenlist==1.5.0
    # via
    #   aiohttp
    #   aiosignal
fsspec==2025.2.0
    # via torch
fuzzywuzzy==0.18.0
    # via bittensor-cli
gitdb==4.0.12
    # via gitpython
gitpython==3.1.44
    # via
    #   bittensor-cli
    #   wandb
google-api-core[grpc]==2.25.1
    # via
    #   google-cloud-core
    #   google-cloud-pubsub
google-auth==2.40.3
    # via
    #   NI-Compute (pyproject.toml)
    #   google-api-core
    #   google-cloud-core
    #   google-cloud-pubsub
google-cloud-core==2.4.3
    # via NI-Compute (pyproject.toml)
google-cloud-pubsub==2.31.1
    # via NI-Compute (pyproject.toml)
googleapis-common-protos[grpc]==1.70.0
    # via
    #   google-api-core
    #   grpc-google-iam-v1
    #   grpcio-status
gputil==1.4.0
    # via NI-Compute (pyproject.toml)
grpc-google-iam-v1==0.14.2
    # via google-cloud-pubsub
grpcio==1.74.0
    # via
    #   google-api-core
    #   google-cloud-pubsub
    #   googleapis-common-protos
    #   grpc-google-iam-v1
    #   grpcio-status
grpcio-status==1.71.2
    # via
    #   google-api-core
    #   google-cloud-pubsub
h11==0.14.0
    # via uvicorn
identify==2.6.8
    # via pre-commit
idna==3.10
    # via
    #   anyio
    #   requests
    #   yarl
igpu==0.1.2
    # via NI-Compute (pyproject.toml)
importlib-metadata==8.7.0
    # via opentelemetry-api
iniconfig==2.0.0
    # via pytest
ipwhois==1.3.0
    # via NI-Compute (pyproject.toml)
jinja2==3.1.5
    # via
    #   bittensor-cli
    #   torch
levenshtein==0.27.1
    # via python-levenshtein
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.2
    # via jinja2
mdurl==0.1.2
    # via markdown-it-py
more-itertools==10.6.0
    # via scalecodec
mpmath==1.3.0
    # via sympy
msgpack==1.1.0
    # via msgpack-numpy-opentensor
msgpack-numpy-opentensor==0.5.0
    # via bittensor
multidict==6.1.0
    # via
    #   aiohttp
    #   yarl
munch==2.5.0
    # via
    #   bittensor
    #   bittensor-wallet
narwhals==1.29.0
    # via plotly
nest-asyncio==1.6.0
    # via bittensor
netaddr==1.3.0
    # via
    #   bittensor
    #   bittensor-cli
networkx==3.4.2
    # via torch
nodeenv==1.9.1
    # via pre-commit
numpy==2.0.2
    # via
    #   NI-Compute (pyproject.toml)
    #   bittensor
    #   bittensor-cli
    #   msgpack-numpy-opentensor
nvidia-cublas-cu12==********
    # via
    #   nvidia-cudnn-cu12
    #   nvidia-cusolver-cu12
    #   torch
nvidia-cuda-cupti-cu12==12.4.127
    # via torch
nvidia-cuda-nvrtc-cu12==12.4.127
    # via torch
nvidia-cuda-runtime-cu12==12.4.127
    # via torch
nvidia-cudnn-cu12==9.1.0.70
    # via torch
nvidia-cufft-cu12==11.2.1.3
    # via torch
nvidia-curand-cu12==10.3.5.147
    # via torch
nvidia-cusolver-cu12==11.6.1.9
    # via torch
nvidia-cusparse-cu12==12.3.1.170
    # via
    #   nvidia-cusolver-cu12
    #   torch
nvidia-ml-py==12.570.86
    # via pynvml
nvidia-nccl-cu12==2.21.5
    # via torch
nvidia-nvjitlink-cu12==12.4.127
    # via
    #   nvidia-cusolver-cu12
    #   nvidia-cusparse-cu12
    #   torch
nvidia-nvtx-cu12==12.4.127
    # via torch
opentelemetry-api==1.36.0
    # via
    #   google-cloud-pubsub
    #   opentelemetry-sdk
    #   opentelemetry-semantic-conventions
opentelemetry-sdk==1.36.0
    # via google-cloud-pubsub
opentelemetry-semantic-conventions==0.57b0
    # via opentelemetry-sdk
packaging==24.2
    # via
    #   bittensor
    #   build
    #   docker
    #   plotly
    #   pyinstaller
    #   pyinstaller-hooks-contrib
    #   pytest
paramiko==3.4.1
    # via NI-Compute (pyproject.toml)
password-strength==0.0.3.post2
    # via bittensor-wallet
pip-tools==7.4.1
    # via NI-Compute (pyproject.toml)
platformdirs==4.3.6
    # via
    #   virtualenv
    #   wandb
plotille==5.0.0
    # via bittensor-cli
plotly==6.0.0
    # via bittensor-cli
pluggy==1.5.0
    # via
    #   allure-python-commons
    #   pytest
pre-commit==4.1.0
    # via NI-Compute (pyproject.toml)
propcache==0.3.0
    # via yarl
proto-plus==1.26.1
    # via
    #   google-api-core
    #   google-cloud-pubsub
protobuf==5.29.3
    # via
    #   google-api-core
    #   google-cloud-pubsub
    #   googleapis-common-protos
    #   grpc-google-iam-v1
    #   grpcio-status
    #   proto-plus
    #   wandb
psutil==5.9.8
    # via
    #   NI-Compute (pyproject.toml)
    #   igpu
    #   wandb
py==1.11.0
    # via retry
py-bip39-bindings==0.1.11
    # via bittensor-wallet
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via google-auth
pycparser==2.22
    # via cffi
pycryptodome==3.21.0
    # via
    #   bittensor
    #   bittensor-cli
pydantic==2.10.6
    # via
    #   bittensor
    #   fastapi
    #   wandb
pydantic-core==2.27.2
    # via pydantic
pyfiglet==1.0.2
    # via NI-Compute (pyproject.toml)
pygments==2.19.1
    # via rich
pyinstaller==6.4.0
    # via NI-Compute (pyproject.toml)
pyinstaller-hooks-contrib==2025.1
    # via pyinstaller
pynacl==1.5.0
    # via paramiko
pynvml==12.0.0
    # via igpu
pyproject-hooks==1.2.0
    # via
    #   build
    #   pip-tools
pytest==8.3.5
    # via
    #   NI-Compute (pyproject.toml)
    #   allure-pytest
    #   bittensor-cli
    #   pytest-cov
pytest-cov==6.0.0
    # via NI-Compute (pyproject.toml)
python-dotenv==1.0.1
    # via NI-Compute (pyproject.toml)
python-levenshtein==0.27.1
    # via
    #   bittensor
    #   bittensor-cli
python-statemachine==2.5.0
    # via bittensor
pywry==0.6.2
    # via bittensor-cli
pyyaml==6.0.2
    # via
    #   bittensor
    #   bittensor-cli
    #   pre-commit
    #   wandb
rapidfuzz==3.12.2
    # via levenshtein
requests==2.31.0
    # via
    #   NI-Compute (pyproject.toml)
    #   bittensor
    #   docker
    #   google-api-core
    #   scalecodec
    #   wandb
retry==0.9.2
    # via bittensor
rich==13.9.4
    # via
    #   bittensor
    #   bittensor-cli
    #   bittensor-wallet
    #   typer
rsa==4.9.1
    # via google-auth
scalecodec==1.2.11
    # via
    #   async-substrate-interface
    #   bittensor
    #   bittensor-cli
sentry-sdk==2.22.0
    # via wandb
setproctitle==1.3.5
    # via
    #   pywry
    #   wandb
shellingham==1.5.4
    # via typer
six==1.17.0
    # via
    #   docker-pycreds
    #   munch
    #   password-strength
smmap==5.0.2
    # via gitdb
sniffio==1.3.1
    # via anyio
starlette==0.37.2
    # via fastapi
sympy==1.13.1
    # via torch
termcolor==2.5.0
    # via bittensor-wallet
toml==0.10.0
    # via bt-decode
toolz==1.0.0
    # via cytoolz
torch==2.5.1
    # via NI-Compute (pyproject.toml)
triton==3.1.0
    # via torch
typer==0.15.2
    # via bittensor-cli
typing-extensions==4.12.2
    # via
    #   anyio
    #   eth-typing
    #   fastapi
    #   opentelemetry-api
    #   opentelemetry-sdk
    #   opentelemetry-semantic-conventions
    #   pydantic
    #   pydantic-core
    #   torch
    #   typer
urllib3==2.3.0
    # via
    #   docker
    #   requests
    #   sentry-sdk
uvicorn==0.34.0
    # via bittensor
virtualenv==20.29.2
    # via pre-commit
wandb==0.19.0
    # via NI-Compute (pyproject.toml)
websockets==15.0
    # via
    #   async-substrate-interface
    #   bittensor
    #   bittensor-cli
wheel==0.45.1
    # via
    #   async-substrate-interface
    #   bittensor
    #   bittensor-cli
    #   pip-tools
xxhash==3.5.0
    # via async-substrate-interface
yarl==1.18.3
    # via aiohttp
zipp==3.23.0
    # via importlib-metadata

# The following packages are considered to be unsafe in a requirements file:
# pip
# setuptools
